-- Criar banco para empresa de teste
CREATE DATABASE erp_empresa_teste
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TEMPLATE template0;

\c erp_empresa_teste;

-- Habilitar extensão para criptografia
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Usu<PERSON><PERSON>s da empresa
CREATE TABLE usuarios (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    senha VARCHAR(255) NOT NULL,
    nivel_acesso VARCHAR(20) DEFAULT 'usuario', -- admin, gerente, usuario
    ativo BOOLEAN DEFAULT TRUE
);

-- Clientes
CREATE TABLE clientes (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(150) NOT NULL,
    telefone VARCHAR(20),
    email VARCHAR(100),
    endereco TEXT
);

-- Produtos
CREATE TABLE produtos (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(150) NOT NULL,
    descricao TEXT,
    preco DECIMAL(10,2) NOT NULL,
    estoque INTEGER DEFAULT 0
);

-- Pedidos
CREATE TABLE pedidos (
    id SERIAL PRIMARY KEY,
    cliente_id INTEGER REFERENCES clientes(id),
    data_pedido TIMESTAMP DEFAULT NOW(),
    valor_total DECIMAL(10,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pendente' -- pendente, processando, concluido, cancelado
);

-- Itens do pedido
CREATE TABLE pedido_itens (
    id SERIAL PRIMARY KEY,
    pedido_id INTEGER REFERENCES pedidos(id) ON DELETE CASCADE,
    produto_id INTEGER REFERENCES produtos(id),
    quantidade INTEGER NOT NULL,
    preco_unitario DECIMAL(10,2) NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL
);

-- Financeiro
CREATE TABLE financeiro (
    id SERIAL PRIMARY KEY,
    descricao VARCHAR(200) NOT NULL,
    tipo VARCHAR(20) NOT NULL, -- receita, despesa
    valor DECIMAL(10,2) NOT NULL,
    data_vencimento DATE NOT NULL,
    data_pagamento DATE,
    status VARCHAR(20) DEFAULT 'pendente', -- pendente, pago, vencido
    pedido_id INTEGER REFERENCES pedidos(id)
);

-- Inserir empresa no banco master
\c erp_master;
INSERT INTO empresas (nome, cnpj, cidade, banco_nome, ativa) 
VALUES ('Empresa Teste', '12.345.678/0001-90', 'Itapira', 'erp_empresa_teste', TRUE);

-- Voltar para o banco da empresa e criar usuário teste
\c erp_empresa_teste;
INSERT INTO usuarios (nome, email, senha, nivel_acesso) 
VALUES ('Admin Teste', '<EMAIL>', crypt('123456', gen_salt('bf')), 'admin');

-- Dados de exemplo
INSERT INTO clientes (nome, telefone, email, endereco) VALUES
('João Silva', '(19) 99999-1111', '<EMAIL>', 'Rua A, 123 - Itapira/SP'),
('Maria Santos', '(19) 99999-2222', '<EMAIL>', 'Rua B, 456 - Itapira/SP');

INSERT INTO produtos (nome, descricao, preco, estoque) VALUES
('Produto A', 'Descrição do produto A', 29.90, 100),
('Produto B', 'Descrição do produto B', 49.90, 50),
('Produto C', 'Descrição do produto C', 19.90, 200);
