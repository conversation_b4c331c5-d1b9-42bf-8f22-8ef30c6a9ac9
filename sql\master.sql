-- Criar banco master
CREATE DATABASE erp_master
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TEMPLATE template0;

\c erp_master;

-- Controle de empresas
CREATE TABLE empresas (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(150) NOT NULL,
    cnpj VARCHAR(20),
    cidade VARCHAR(100) DEFAULT 'Itapira',
    banco_nome VARCHAR(100) NOT NULL, -- ex: erp_empresa_1
    ativa BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT NOW()
);

-- <PERSON><PERSON><PERSON><PERSON>s master (administradores do SaaS)
CREATE TABLE admins (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    senha VARCHAR(255) NOT NULL,
    nivel_acesso VARCHAR(20) DEFAULT 'admin'
);

-- Habilitar extensão para criptografia
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- <PERSON><PERSON><PERSON>rio admin inicial (senha: 123456)
INSERT INTO admins (nome, email, senha)
VALUES ('Administrador', '<EMAIL>', crypt('123456', gen_salt('bf')));
